# PDF面单命名转换器 - 打包说明

## 打包准备

### 环境要求
- Python 3.8+ (您使用的Python 3.11完全支持)
- PyQt6
- PyInstaller

### 安装PyInstaller
```bash
pip install pyinstaller
```

## 打包方法

### 方法一：使用批处理脚本（推荐）
直接双击 `build.bat` 文件，脚本会自动：
1. 检查Python环境
2. 安装必要的依赖包
3. 执行打包命令
4. 显示打包结果

### 方法二：手动打包
```bash
# 清理旧的打包文件
rmdir /s /q dist build

# 执行打包
pyinstaller --clean pdf_converter.spec
```

### 方法三：一键打包命令
```bash
pyinstaller --onefile --windowed --name="PDF面单命名转换器" main_simple.py
```

## 打包配置说明

### .spec文件特点
- **单文件打包**：生成单个.exe文件，便于分发
- **无控制台**：运行时不显示黑色命令行窗口
- **优化体积**：排除不必要的模块，减小文件大小
- **包含版本信息**：Windows文件属性中显示版本信息

### 打包优化
- 使用 `main_simple.py` 作为入口点（移除了复杂的日志功能）
- 排除了大量不需要的Python标准库模块
- 启用UPX压缩（如果可用）

## 打包结果

### 输出文件
- **位置**：`dist/PDF面单命名转换器.exe`
- **大小**：约50-80MB（取决于PyQt6版本）
- **类型**：Windows可执行文件

### 分发说明
- 生成的.exe文件可以在任何Windows电脑上运行
- 无需安装Python或其他依赖
- 支持Windows 7/8/10/11

## 测试打包结果

### 功能验证
1. 运行生成的.exe文件
2. 选择包含面单PDF的文件夹
3. 测试平台单号和派送单号提取
4. 验证批量重命名功能

### 性能检查
- 启动速度：首次启动可能较慢（10-30秒）
- 运行流畅度：应与Python版本相同
- 内存占用：约100-200MB

## 常见问题

### 问题1：打包失败
**可能原因**：
- PyInstaller版本不兼容
- 缺少必要的依赖包
- Python环境配置问题

**解决方案**：
```bash
# 更新PyInstaller
pip install --upgrade pyinstaller

# 重新安装PyQt6
pip uninstall PyQt6
pip install PyQt6

# 清理缓存重新打包
pyinstaller --clean pdf_converter.spec
```

### 问题2：exe文件过大
**优化方案**：
- 已在.spec文件中排除不必要模块
- 可以进一步排除更多模块（需要测试）
- 使用UPX压缩工具

### 问题3：运行时错误
**检查项目**：
- 确保所有必要的模块都包含在hiddenimports中
- 检查是否有硬编码的文件路径
- 验证PyQt6版本兼容性

## 高级配置

### 自定义图标
如果您有图标文件（.ico格式），可以在.spec文件中取消注释：
```python
icon='icon.ico',
```

### 添加文件资源
如果需要包含额外文件，在.spec文件的datas部分添加：
```python
datas=[
    ('config.ini', '.'),
    ('templates/', 'templates/'),
],
```

### 调试模式
如果需要调试打包问题，可以临时启用控制台：
```python
console=True,  # 显示控制台窗口
debug=True,    # 启用调试信息
```

## 分发建议

### 文件组织
```
发布包/
├── PDF面单命名转换器.exe    # 主程序
├── 使用说明.pdf            # 用户手册
├── 示例文件/               # 示例面单文件
└── README.txt              # 简要说明
```

### 用户指南
为最终用户提供简单的使用指南：
1. 双击运行.exe文件
2. 选择包含面单PDF的文件夹
3. 选择提取模式并预览
4. 执行批量重命名

---

**打包完成后，您将获得一个完全独立的Windows可执行文件，可以在任何Windows电脑上运行，无需安装Python环境！**
