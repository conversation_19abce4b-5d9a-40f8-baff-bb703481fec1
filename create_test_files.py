# -*- coding: utf-8 -*-
"""
创建测试文件脚本
用于生成示例PDF文件来测试转换器功能
"""

import os
try:
    from pathlib import Path
except ImportError:
    # For older Python versions, create a simple Path-like class
    class Path:
        def __init__(self, path):
            self.path = str(path)

        def __truediv__(self, other):
            return Path(os.path.join(self.path, str(other)))

        def __str__(self):
            return self.path

        def exists(self):
            return os.path.exists(self.path)

        def mkdir(self, exist_ok=False):
            if not self.exists():
                os.makedirs(self.path)
            elif not exist_ok:
                raise OSError("Directory already exists")

        def absolute(self):
            return Path(os.path.abspath(self.path))


def create_test_files():
    """创建测试文件"""
    # 创建测试文件夹
    test_folder = Path("test_pdfs")
    test_folder.mkdir(exist_ok=True)
    
    # 测试文件列表
    test_files = [
        # 有效的面单文件
        "面单_GSU1WQ30B000PSY_GF6457466550016.pdf",
        "订单_ABC123456789_GF1234567890123.pdf",
        "快递_XYZ987654321_GF9876543210987.pdf",
        "发货单_DEF456789012_GF4567890123456.pdf",
        "运单_HIJ789012345_GF7890123456789.pdf",
        
        # 只有平台单号的文件
        "平台订单_MNO012345678.pdf",
        "电商单_PQR345678901.pdf",
        
        # 只有派送单号的文件
        "派送_GF1111222233334.pdf",
        "配送_GF5555666677778.pdf",
        
        # 无效文件（不会被处理）
        "普通文档.pdf",
        "说明书.pdf",
        "invalid_format.pdf",
        "no_numbers_here.pdf",
    ]
    
    print("Creating test files in folder: {}".format(test_folder.absolute()))
    print("=" * 50)

    created_count = 0
    for filename in test_files:
        file_path = test_folder / filename

        # Create empty PDF files (for testing filename only)
        try:
            with open(str(file_path), 'w', encoding='utf-8') as f:
                f.write("%PDF-1.4\n% Test file: {}\n%%EOF".format(filename))

            print("✓ Created: {}".format(filename))
            created_count += 1

        except Exception as e:
            print("✗ Failed to create: {} - Error: {}".format(filename, e))

    print("=" * 50)
    print("Test files created! Total: {} files".format(created_count))
    print("Test folder location: {}".format(test_folder.absolute()))
    print()
    print("Usage instructions:")
    print("1. Run 'python main.py' to start the program")
    print("2. Click 'Select Folder' button")
    print("3. Select the newly created 'test_pdfs' folder")
    print("4. Choose extraction mode (platform number or delivery number)")
    print("5. Click 'Preview Processing' to see rename results")
    print("6. Click 'Start Processing' to execute batch rename")
    print()
    print("Note: These are test files, safe to delete after renaming")


def show_test_file_analysis():
    """显示测试文件分析"""
    from pdf_parser import PDFParser
    
    test_folder = Path("test_pdfs")
    if not test_folder.exists():
        print("Test folder does not exist, please create test files first")
        return

    parser = PDFParser()

    print("Test File Analysis Report")
    print("=" * 60)

    # Get all PDF files
    all_pdfs = parser.get_pdf_files(str(test_folder))
    valid_pdfs = parser.get_valid_pdf_files(str(test_folder))

    print("Total PDF files: {}".format(len(all_pdfs)))
    print("Valid waybill files: {}".format(len(valid_pdfs)))
    print()

    print("Detailed file analysis:")
    print("-" * 60)
    
    for pdf_file in all_pdfs:
        platform_num = parser.extract_platform_number(pdf_file)
        delivery_num = parser.extract_delivery_number(pdf_file)
        is_valid = parser.is_valid_pdf_name(pdf_file)

        status = "✓ Valid" if is_valid else "✗ Invalid"

        print("File: {}".format(pdf_file))
        print("  Status: {}".format(status))
        print("  Platform number: {}".format(platform_num or 'Not found'))
        print("  Delivery number: {}".format(delivery_num or 'Not found'))
        print()

    # Preview rename results
    print("Platform number rename preview:")
    print("-" * 30)
    platform_preview = parser.preview_rename(str(test_folder), 'platform')
    for old_name, new_name in platform_preview:
        print("  {} → {}".format(old_name, new_name))

    print()
    print("Delivery number rename preview:")
    print("-" * 30)
    delivery_preview = parser.preview_rename(str(test_folder), 'delivery')
    for old_name, new_name in delivery_preview:
        print("  {} → {}".format(old_name, new_name))


def clean_test_files():
    """Clean test files"""
    test_folder = Path("test_pdfs")

    if not test_folder.exists():
        print("Test folder does not exist")
        return

    try:
        import shutil
        shutil.rmtree(test_folder)
        print("Deleted test folder: {}".format(test_folder.absolute()))
    except Exception as e:
        print("Failed to delete test folder: {}".format(e))


def main():
    """Main function"""
    print("PDF Waybill Naming Converter - Test File Management Tool")
    print("=" * 50)
    print("1. Create test files")
    print("2. Analyze test files")
    print("3. Clean test files")
    print("4. Exit")
    print()

    while True:
        try:
            choice = raw_input("Please select operation (1-4): ").strip()
        except NameError:
            # Python 3
            choice = input("Please select operation (1-4): ").strip()

        if choice == '1':
            create_test_files()
            break
        elif choice == '2':
            show_test_file_analysis()
            break
        elif choice == '3':
            clean_test_files()
            break
        elif choice == '4':
            print("Exit")
            break
        else:
            print("Invalid choice, please enter 1-4")


if __name__ == "__main__":
    main()
