# PDF面单命名转换器

一个用于批量重命名PDF面单文件的工具，可以从文件名中提取平台单号或派送单号，并将文件重命名为对应的单号。

## 功能特点

- 🎯 **智能识别**: 自动识别符合面单命名规则的PDF文件
- 🔄 **双模式提取**: 支持提取平台单号和派送单号两种模式
- 👀 **预览功能**: 处理前可预览重命名结果
- 📊 **批量处理**: 一键批量重命名多个文件
- 🎨 **美观界面**: 现代化的图形用户界面
- ⚡ **多线程**: 使用多线程处理，避免界面卡顿
- 🛡️ **安全保护**: 处理前确认对话框，避免误操作

## 支持的文件名格式

工具能够识别以下格式的PDF文件名：

- `面单_GSU1WQ30B000PSY_GF6457466550016.pdf`
- `订单_ABC123456789_GF1234567890123.pdf`
- 其他包含平台单号和派送单号的格式

### 单号规则

- **平台单号**: 2-4个字母开头，后跟8-15位字母数字组合 (如: GSU1WQ30B000PSY)
- **派送单号**: GF开头，后跟10-15位数字 (如: GF6457466550016)

## 安装要求

- Python 3.8+
- PyQt6

## 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序：
   ```bash
   python main.py
   ```

2. 使用步骤：
   - 点击"选择文件夹"按钮，选择包含PDF文件的文件夹
   - 在"识别面单"列中查看找到的符合规则的PDF文件
   - 选择提取模式：
     - "提取平台单号"：将文件重命名为平台单号
     - "提取派送单号"：将文件重命名为派送单号
   - 点击"预览处理"查看重命名结果
   - 确认无误后，点击"开始处理"执行批量重命名
   - 在"处理结果"列中查看处理结果

## 界面说明

### 左侧控制面板
- **文件夹选择**: 选择要处理的PDF文件所在文件夹
- **提取选项**: 选择提取平台单号还是派送单号
- **操作按钮**: 预览处理和开始处理按钮

### 右侧文件列表
- **识别面单**: 显示找到的符合规则的PDF文件
- **预览处理**: 显示重命名预览结果
- **处理结果**: 显示处理完成后的结果和错误信息

## 注意事项

- ⚠️ 文件重命名操作不可撤销，请在处理前仔细检查预览结果
- 📁 如果目标文件名已存在，程序会自动添加序号避免冲突
- 🔍 只有符合面单命名规则的PDF文件才会被识别和处理
- 💾 建议在处理前备份重要文件

## 错误处理

程序具有完善的错误处理机制：
- 文件访问权限问题
- 文件名冲突处理
- 无效文件格式过滤
- 处理过程异常捕获

## 技术特性

- 使用PyQt6构建现代化GUI
- 多线程处理避免界面冻结
- 正则表达式精确匹配单号格式
- 完整的异常处理和用户反馈

## 版本信息

- 版本: 1.0
- 开发语言: Python 3
- GUI框架: PyQt6
- 支持平台: Windows, macOS, Linux

## 许可证

本项目仅供学习和个人使用。
