# -*- coding: utf-8 -*-
"""
PDF解析器测试文件
测试PDF文件名解析功能的正确性
"""

import unittest
import os
import tempfile
import shutil
from pathlib import Path
from pdf_parser import PDFParser


class TestPDFParser(unittest.TestCase):
    """PDF解析器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.parser = PDFParser()
        
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试PDF文件（空文件，只测试文件名）
        self.test_files = [
            "面单_GSU1WQ30B000PSY_GF6457466550016.pdf",
            "订单_ABC123456789_GF1234567890123.pdf",
            "快递_XYZ987654321_GF9876543210987.pdf",
            "invalid_file.pdf",  # 无效文件
            "no_extension",  # 无扩展名
            "面单_INVALID_FORMAT.pdf",  # 格式不正确
            "normal_document.pdf",  # 普通文档
        ]
        
        for filename in self.test_files:
            file_path = Path(self.test_dir) / filename
            file_path.touch()  # 创建空文件
    
    def tearDown(self):
        """测试后的清理工作"""
        # 删除临时测试目录
        shutil.rmtree(self.test_dir)
    
    def test_extract_platform_number(self):
        """测试平台单号提取"""
        test_cases = [
            ("面单_GSU1WQ30B000PSY_GF6457466550016.pdf", "GSU1WQ30B000PSY"),
            ("订单_ABC123456789_GF1234567890123.pdf", "ABC123456789"),
            ("快递_XYZ987654321_GF9876543210987.pdf", "XYZ987654321"),
            ("invalid_file.pdf", None),
            ("normal_document.pdf", None),
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.parser.extract_platform_number(filename)
                self.assertEqual(result, expected, 
                               f"文件 {filename} 的平台单号提取结果不正确")
    
    def test_extract_delivery_number(self):
        """测试派送单号提取"""
        test_cases = [
            ("面单_GSU1WQ30B000PSY_GF6457466550016.pdf", "GF6457466550016"),
            ("订单_ABC123456789_GF1234567890123.pdf", "GF1234567890123"),
            ("快递_XYZ987654321_GF9876543210987.pdf", "GF9876543210987"),
            ("invalid_file.pdf", None),
            ("normal_document.pdf", None),
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.parser.extract_delivery_number(filename)
                self.assertEqual(result, expected, 
                               f"文件 {filename} 的派送单号提取结果不正确")
    
    def test_is_valid_pdf_name(self):
        """测试PDF文件名有效性检查"""
        test_cases = [
            ("面单_GSU1WQ30B000PSY_GF6457466550016.pdf", True),
            ("订单_ABC123456789_GF1234567890123.pdf", True),
            ("快递_XYZ987654321_GF9876543210987.pdf", True),
            ("invalid_file.pdf", False),
            ("no_extension", False),
            ("面单_INVALID_FORMAT.pdf", False),
            ("normal_document.pdf", False),
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.parser.is_valid_pdf_name(filename)
                self.assertEqual(result, expected, 
                               f"文件 {filename} 的有效性检查结果不正确")
    
    def test_get_pdf_files(self):
        """测试获取PDF文件列表"""
        pdf_files = self.parser.get_pdf_files(self.test_dir)
        
        # 检查返回的文件数量
        expected_pdf_count = len([f for f in self.test_files if f.endswith('.pdf')])
        self.assertEqual(len(pdf_files), expected_pdf_count)
        
        # 检查所有PDF文件都被包含
        for filename in self.test_files:
            if filename.endswith('.pdf'):
                self.assertIn(filename, pdf_files)
    
    def test_get_valid_pdf_files(self):
        """测试获取有效PDF文件列表"""
        valid_files = self.parser.get_valid_pdf_files(self.test_dir)
        
        # 预期的有效文件
        expected_valid = [
            "面单_GSU1WQ30B000PSY_GF6457466550016.pdf",
            "订单_ABC123456789_GF1234567890123.pdf",
            "快递_XYZ987654321_GF9876543210987.pdf",
        ]
        
        self.assertEqual(len(valid_files), len(expected_valid))
        for filename in expected_valid:
            self.assertIn(filename, valid_files)
    
    def test_preview_rename_platform(self):
        """测试平台单号重命名预览"""
        preview_results = self.parser.preview_rename(self.test_dir, 'platform')
        
        expected_results = [
            ("面单_GSU1WQ30B000PSY_GF6457466550016.pdf", "GSU1WQ30B000PSY.pdf"),
            ("订单_ABC123456789_GF1234567890123.pdf", "ABC123456789.pdf"),
            ("快递_XYZ987654321_GF9876543210987.pdf", "XYZ987654321.pdf"),
        ]
        
        self.assertEqual(len(preview_results), len(expected_results))
        for expected in expected_results:
            self.assertIn(expected, preview_results)
    
    def test_preview_rename_delivery(self):
        """测试派送单号重命名预览"""
        preview_results = self.parser.preview_rename(self.test_dir, 'delivery')
        
        expected_results = [
            ("面单_GSU1WQ30B000PSY_GF6457466550016.pdf", "GF6457466550016.pdf"),
            ("订单_ABC123456789_GF1234567890123.pdf", "GF1234567890123.pdf"),
            ("快递_XYZ987654321_GF9876543210987.pdf", "GF9876543210987.pdf"),
        ]
        
        self.assertEqual(len(preview_results), len(expected_results))
        for expected in expected_results:
            self.assertIn(expected, preview_results)
    
    def test_rename_files(self):
        """测试文件重命名功能"""
        # 测试平台单号重命名
        success_count, fail_count, errors = self.parser.rename_files(self.test_dir, 'platform')
        
        # 检查重命名结果
        self.assertEqual(success_count, 3)  # 应该成功重命名3个文件
        self.assertEqual(fail_count, 0)     # 应该没有失败
        self.assertEqual(len(errors), 0)    # 应该没有错误
        
        # 检查文件是否真的被重命名了
        renamed_files = self.parser.get_pdf_files(self.test_dir)
        expected_renamed = ["GSU1WQ30B000PSY.pdf", "ABC123456789.pdf", "XYZ987654321.pdf"]
        
        for expected_name in expected_renamed:
            self.assertIn(expected_name, renamed_files)
    
    def test_empty_folder(self):
        """测试空文件夹处理"""
        empty_dir = tempfile.mkdtemp()
        try:
            pdf_files = self.parser.get_pdf_files(empty_dir)
            self.assertEqual(len(pdf_files), 0)
            
            valid_files = self.parser.get_valid_pdf_files(empty_dir)
            self.assertEqual(len(valid_files), 0)
            
            preview_results = self.parser.preview_rename(empty_dir, 'platform')
            self.assertEqual(len(preview_results), 0)
        finally:
            shutil.rmtree(empty_dir)
    
    def test_nonexistent_folder(self):
        """测试不存在的文件夹处理"""
        nonexistent_path = "/path/that/does/not/exist"
        
        pdf_files = self.parser.get_pdf_files(nonexistent_path)
        self.assertEqual(len(pdf_files), 0)
        
        valid_files = self.parser.get_valid_pdf_files(nonexistent_path)
        self.assertEqual(len(valid_files), 0)


class TestRegexPatterns(unittest.TestCase):
    """正则表达式模式测试类"""
    
    def setUp(self):
        self.parser = PDFParser()
    
    def test_platform_pattern_variations(self):
        """测试平台单号模式的各种变化"""
        test_cases = [
            # 有效的平台单号
            ("GSU1WQ30B000PSY", True),
            ("ABC123456789", True),
            ("XY1234567890AB", True),
            ("ABCD12345678", True),
            
            # 无效的平台单号
            ("A123456789", False),      # 太短的字母前缀
            ("ABCDE123456789", False),  # 太长的字母前缀
            ("ABC1234567", False),      # 太短的总长度
            ("ABC1234567890123456", False),  # 太长的总长度
            ("123456789", False),       # 没有字母前缀
        ]
        
        for test_string, should_match in test_cases:
            with self.subTest(test_string=test_string):
                result = self.parser.extract_platform_number(f"test_{test_string}_test.pdf")
                if should_match:
                    self.assertEqual(result, test_string)
                else:
                    self.assertIsNone(result)
    
    def test_delivery_pattern_variations(self):
        """测试派送单号模式的各种变化"""
        test_cases = [
            # 有效的派送单号
            ("GF1234567890", True),
            ("GF12345678901234", True),
            ("GF123456789012345", True),
            
            # 无效的派送单号
            ("GF123456789", False),      # 太短
            ("GF1234567890123456", False),  # 太长
            ("GG1234567890", False),     # 不是GF开头
            ("FG1234567890", False),     # 不是GF开头
            ("1234567890", False),       # 没有GF前缀
        ]
        
        for test_string, should_match in test_cases:
            with self.subTest(test_string=test_string):
                result = self.parser.extract_delivery_number(f"test_{test_string}_test.pdf")
                if should_match:
                    self.assertEqual(result, test_string)
                else:
                    self.assertIsNone(result)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
