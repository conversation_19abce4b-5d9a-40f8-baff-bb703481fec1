# -*- coding: utf-8 -*-

import os

def create_test_files():
    """Create test files directly"""
    # Create test folder
    test_folder = "test_pdfs"
    if not os.path.exists(test_folder):
        os.makedirs(test_folder)
    
    # Test files list
    test_files = [
        # Valid waybill files
        "waybill_GSU1WQ30B000PSY_GF6457466550016.pdf",
        "order_ABC123456789_GF1234567890123.pdf",
        "express_XYZ987654321_GF9876543210987.pdf",
        "shipping_DEF456789012_GF4567890123456.pdf",
        "delivery_HIJ789012345_GF7890123456789.pdf",
        
        # Files with platform number only
        "platform_order_MNO012345678.pdf",
        "ecommerce_PQR345678901.pdf",
        
        # Files with delivery number only
        "delivery_GF1111222233334.pdf",
        "shipping_GF5555666677778.pdf",
        
        # Invalid files (won't be processed)
        "normal_document.pdf",
        "manual.pdf",
        "invalid_format.pdf",
        "no_numbers_here.pdf",
    ]
    
    print("Creating test files in folder: {}".format(os.path.abspath(test_folder)))
    print("=" * 50)
    
    created_count = 0
    for filename in test_files:
        file_path = os.path.join(test_folder, filename)
        
        # Create empty PDF files (for testing filename only)
        try:
            with open(file_path, 'w') as f:
                f.write("%PDF-1.4\n% Test file: {}\n%%EOF".format(filename))
            
            print("Created: {}".format(filename))
            created_count += 1
            
        except Exception as e:
            print("Failed to create: {} - Error: {}".format(filename, e))
    
    print("=" * 50)
    print("Test files created! Total: {} files".format(created_count))
    print("Test folder location: {}".format(os.path.abspath(test_folder)))
    print()
    print("Usage instructions:")
    print("1. Run 'python main.py' to start the program")
    print("2. Click 'Select Folder' button")
    print("3. Select the 'test_pdfs' folder")
    print("4. Choose extraction mode")
    print("5. Click 'Preview Processing' to see results")
    print("6. Click 'Start Processing' to rename files")

if __name__ == "__main__":
    create_test_files()
