# PDF面单命名转换器 - 项目文件清单

## 核心程序文件

### 主程序
- `main.py` - 原始主程序入口（包含完整功能）
- `main_simple.py` - 简化版主程序（专为打包优化）
- `main_window.py` - 完整版UI界面实现
- `pdf_parser.py` - PDF文件名解析核心逻辑

### 配置和工具
- `config.py` - 应用程序配置文件
- `logger.py` - 日志模块（打包时可忽略）
- `requirements.txt` - Python依赖包列表

## 打包相关文件

### 打包配置
- `pdf_converter.spec` - PyInstaller打包配置文件
- `version_info.txt` - Windows可执行文件版本信息
- `build.bat` - 自动打包脚本

### 打包说明
- `打包说明.md` - 详细的打包操作指南

## 测试文件

### 测试脚本
- `test_basic.py` - 基础功能测试
- `test_pdf_parser.py` - 完整单元测试（可能有编码问题）
- `simple_test.py` - 简单测试脚本
- `analyze_test.py` - 测试文件分析工具

### 测试数据
- `create_test_simple.py` - 创建测试PDF文件
- `create_test_files.py` - 完整版测试文件创建工具
- `test_pdfs/` - 测试文件夹（包含20个测试PDF文件）

## 文档文件

### 用户文档
- `README.md` - 英文项目说明
- `项目说明.md` - 中文项目详细说明
- `使用说明.md` - 用户操作指南

### 运行脚本
- `run.bat` - Windows运行脚本

## 打包使用建议

### 推荐打包流程
1. 使用 `main_simple.py` 作为入口点（已在.spec中配置）
2. 运行 `build.bat` 进行自动打包
3. 生成的exe文件位于 `dist/` 目录

### 文件说明
- **main_simple.py**：专为打包优化，移除了复杂的日志和配置依赖
- **pdf_converter.spec**：精心配置的打包规范，排除不必要模块
- **version_info.txt**：为exe文件添加版本信息和属性

### 打包后文件结构
```
dist/
└── PDF面单命名转换器.exe    # 单文件可执行程序（约50-80MB）
```

## 核心功能确认

### ✅ 已实现功能
- 智能识别以"面单"开头的PDF文件
- 按下划线分割提取平台单号和派送单号
- 根据用户选择预览提取结果
- 批量重命名文件功能
- 美观的PyQt6图形界面
- 完善的错误处理和用户反馈

### 🎯 打包优化
- 移除了复杂的日志系统（避免打包问题）
- 简化了配置依赖
- 优化了模块导入
- 减小了最终文件大小

## 使用流程

### 开发环境运行
```bash
python main.py          # 完整版（开发调试用）
python main_simple.py   # 简化版（打包用）
```

### 打包分发
```bash
# 自动打包
build.bat

# 手动打包
pyinstaller --clean pdf_converter.spec
```

### 最终用户
- 直接运行 `PDF面单命名转换器.exe`
- 无需安装任何依赖
- 支持所有Windows系统

---

**项目已完全准备好进行打包！您可以直接运行 `build.bat` 来生成单文件可执行程序。**
