# -*- coding: utf-8 -*-
"""
PDF面单命名转换器日志模块
提供统一的日志记录功能
"""

import logging
import logging.handlers
import os
from datetime import datetime
from config import LOG_CONFIG


class Logger:
    """日志管理器"""
    
    def __init__(self, name="PDFConverter"):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志配置"""
        if not LOG_CONFIG['enable_logging']:
            return
        
        # 设置日志级别
        level = getattr(logging, LOG_CONFIG['log_level'].upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器（带轮转）
        try:
            file_handler = logging.handlers.RotatingFileHandler(
                LOG_CONFIG['log_file'],
                maxBytes=LOG_CONFIG['max_log_size'],
                backupCount=LOG_CONFIG['backup_count'],
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        except Exception as e:
            self.logger.warning(f"无法创建文件日志处理器: {e}")
    
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message):
        """记录调试日志"""
        self.logger.debug(message)
    
    def critical(self, message):
        """记录严重错误日志"""
        self.logger.critical(message)
    
    def log_operation(self, operation, details=None):
        """记录操作日志"""
        message = f"操作: {operation}"
        if details:
            message += f" - 详情: {details}"
        self.info(message)
    
    def log_file_operation(self, operation, filename, result="成功"):
        """记录文件操作日志"""
        message = f"文件操作: {operation} - 文件: {filename} - 结果: {result}"
        if result == "成功":
            self.info(message)
        else:
            self.error(message)
    
    def log_batch_operation(self, operation, total_count, success_count, fail_count):
        """记录批量操作日志"""
        message = (f"批量操作: {operation} - "
                  f"总数: {total_count}, 成功: {success_count}, 失败: {fail_count}")
        if fail_count == 0:
            self.info(message)
        else:
            self.warning(message)
    
    def log_exception(self, exception, context=""):
        """记录异常日志"""
        message = f"异常发生"
        if context:
            message += f" - 上下文: {context}"
        message += f" - 异常类型: {type(exception).__name__} - 异常信息: {str(exception)}"
        self.error(message)


# 创建全局日志实例
app_logger = Logger("PDFConverter")

# 便捷函数
def log_info(message):
    """记录信息日志"""
    app_logger.info(message)

def log_warning(message):
    """记录警告日志"""
    app_logger.warning(message)

def log_error(message):
    """记录错误日志"""
    app_logger.error(message)

def log_debug(message):
    """记录调试日志"""
    app_logger.debug(message)

def log_operation(operation, details=None):
    """记录操作日志"""
    app_logger.log_operation(operation, details)

def log_file_operation(operation, filename, result="成功"):
    """记录文件操作日志"""
    app_logger.log_file_operation(operation, filename, result)

def log_batch_operation(operation, total_count, success_count, fail_count):
    """记录批量操作日志"""
    app_logger.log_batch_operation(operation, total_count, success_count, fail_count)

def log_exception(exception, context=""):
    """记录异常日志"""
    app_logger.log_exception(exception, context)


# 装饰器：自动记录函数调用
def log_function_call(func):
    """装饰器：自动记录函数调用"""
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        log_debug(f"调用函数: {func_name}")
        try:
            result = func(*args, **kwargs)
            log_debug(f"函数 {func_name} 执行成功")
            return result
        except Exception as e:
            log_exception(e, f"函数 {func_name} 执行失败")
            raise
    return wrapper


# 上下文管理器：记录操作时间
class LogOperationTime:
    """上下文管理器：记录操作执行时间"""
    
    def __init__(self, operation_name):
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        log_info(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            log_info(f"完成执行: {self.operation_name} - 耗时: {duration:.2f}秒")
        else:
            log_error(f"执行失败: {self.operation_name} - 耗时: {duration:.2f}秒 - 错误: {exc_val}")


# 测试日志功能
if __name__ == "__main__":
    # 测试基本日志功能
    log_info("这是一条信息日志")
    log_warning("这是一条警告日志")
    log_error("这是一条错误日志")
    log_debug("这是一条调试日志")
    
    # 测试操作日志
    log_operation("选择文件夹", "/path/to/folder")
    log_file_operation("重命名", "test.pdf", "成功")
    log_batch_operation("批量重命名", 10, 8, 2)
    
    # 测试异常日志
    try:
        raise ValueError("测试异常")
    except Exception as e:
        log_exception(e, "测试上下文")
    
    # 测试时间记录
    with LogOperationTime("测试操作"):
        import time
        time.sleep(1)
    
    print("日志测试完成")
