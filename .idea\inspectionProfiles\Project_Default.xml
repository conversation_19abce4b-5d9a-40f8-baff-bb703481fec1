<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="enctype" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="2">
            <item index="0" class="java.lang.String" itemvalue="2.7" />
            <item index="1" class="java.lang.String" itemvalue="3.13" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="12">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="scikit_image" />
            <item index="2" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="3" class="java.lang.String" itemvalue="GDAL" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="volcengine-ark-sdk" />
            <item index="7" class="java.lang.String" itemvalue="PyQt6" />
            <item index="8" class="java.lang.String" itemvalue="pywin32" />
            <item index="9" class="java.lang.String" itemvalue="PyMuPDF" />
            <item index="10" class="java.lang.String" itemvalue="PyQt6-Qt6" />
            <item index="11" class="java.lang.String" itemvalue="PyQt6-sip" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="SqlResolveInspection" enabled="true" level="INFORMATION" enabled_by_default="true" />
  </profile>
</component>