# -*- coding: utf-8 -*-
"""
PDF面单命名转换器主界面
使用PyQt6实现的图形用户界面
"""
import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QRadioButton, QButtonGroup, QListWidget, QListWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QLabel, QSplitter,
    QGroupBox, QFrame, QStatusBar
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from pdf_parser import PDFParser


class FileProcessThread(QThread):
    """文件处理线程，避免UI阻塞"""
    progress_updated = pyqtSignal(int)
    process_completed = pyqtSignal(int, int, list)
    
    def __init__(self, folder_path, extract_type):
        super().__init__()
        self.folder_path = folder_path
        self.extract_type = extract_type
        self.parser = PDFParser()
    
    def run(self):
        """执行文件重命名处理"""
        try:
            success_count, fail_count, errors = self.parser.rename_files(
                self.folder_path, self.extract_type
            )
            self.process_completed.emit(success_count, fail_count, errors)
        except Exception as e:
            self.process_completed.emit(0, 0, [f"处理过程中发生错误: {str(e)}"])


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.parser = PDFParser()
        self.current_folder = ""
        self.preview_results = []
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("PDF面单命名转换器 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(800, 600)
        
        # 设置应用程序样式
        self.setup_styles()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建左侧控制面板
        self.create_control_panel(main_layout)
        
        # 创建右侧文件列表区域
        self.create_file_lists(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
        
    def setup_styles(self):
        """设置应用程序样式"""
        # 设置应用程序字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        
        # 设置样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QRadioButton {
                spacing: 5px;
                color: #333333;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
    
    def create_control_panel(self, main_layout):
        """创建左侧控制面板"""
        control_frame = QFrame()
        control_frame.setFixedWidth(250)
        control_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        control_layout = QVBoxLayout(control_frame)
        
        # 文件夹选择组
        folder_group = QGroupBox("文件夹选择")
        folder_layout = QVBoxLayout(folder_group)
        
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.setMinimumHeight(40)
        folder_layout.addWidget(self.select_folder_btn)
        
        self.folder_label = QLabel("未选择文件夹")
        self.folder_label.setWordWrap(True)
        self.folder_label.setStyleSheet("color: #666; padding: 5px;")
        folder_layout.addWidget(self.folder_label)
        
        control_layout.addWidget(folder_group)
        
        # 提取选项组
        extract_group = QGroupBox("提取选项")
        extract_layout = QVBoxLayout(extract_group)
        
        self.extract_group = QButtonGroup()
        self.platform_radio = QRadioButton("提取平台单号")
        self.delivery_radio = QRadioButton("提取派送单号")
        self.platform_radio.setChecked(True)  # 默认选择平台单号
        
        self.extract_group.addButton(self.platform_radio, 0)
        self.extract_group.addButton(self.delivery_radio, 1)
        
        extract_layout.addWidget(self.platform_radio)
        extract_layout.addWidget(self.delivery_radio)
        
        control_layout.addWidget(extract_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QVBoxLayout(action_group)
        
        self.preview_btn = QPushButton("预览处理")
        self.preview_btn.setEnabled(False)
        self.preview_btn.setMinimumHeight(35)
        action_layout.addWidget(self.preview_btn)
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setEnabled(False)
        self.process_btn.setMinimumHeight(35)
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
        """)
        action_layout.addWidget(self.process_btn)
        
        control_layout.addWidget(action_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 添加弹性空间
        control_layout.addStretch()
        
        main_layout.addWidget(control_frame)
    
    def create_file_lists(self, main_layout):
        """创建右侧文件列表区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 识别面单列
        identified_group = QGroupBox("识别面单")
        identified_layout = QVBoxLayout(identified_group)
        self.identified_list = QListWidget()
        self.identified_list.setAlternatingRowColors(True)
        identified_layout.addWidget(self.identified_list)
        
        # 预览处理列
        preview_group = QGroupBox("预览处理")
        preview_layout = QVBoxLayout(preview_group)
        self.preview_list = QListWidget()
        self.preview_list.setAlternatingRowColors(True)
        preview_layout.addWidget(self.preview_list)
        
        # 处理结果列
        result_group = QGroupBox("处理结果")
        result_layout = QVBoxLayout(result_group)
        self.result_list = QListWidget()
        self.result_list.setAlternatingRowColors(True)
        result_layout.addWidget(self.result_list)
        
        splitter.addWidget(identified_group)
        splitter.addWidget(preview_group)
        splitter.addWidget(result_group)
        
        # 设置分割器比例
        splitter.setSizes([300, 300, 300])
        
        main_layout.addWidget(splitter)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
    def setup_connections(self):
        """设置信号连接"""
        self.select_folder_btn.clicked.connect(self.select_folder)
        self.preview_btn.clicked.connect(self.preview_processing)
        self.process_btn.clicked.connect(self.start_processing)
        self.extract_group.buttonClicked.connect(self.on_extract_option_changed)

    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择包含PDF文件的文件夹", ""
        )

        if folder_path:
            self.current_folder = folder_path
            self.folder_label.setText(f"已选择: {os.path.basename(folder_path)}")
            self.load_pdf_files()
            self.preview_btn.setEnabled(True)
            self.status_bar.showMessage(f"已选择文件夹: {folder_path}")

    def load_pdf_files(self):
        """加载PDF文件到识别面单列表"""
        if not self.current_folder:
            return

        # 清空列表
        self.identified_list.clear()
        self.preview_list.clear()
        self.result_list.clear()

        # 获取有效的PDF文件
        valid_pdfs = self.parser.get_valid_pdf_files(self.current_folder)

        if not valid_pdfs:
            self.identified_list.addItem("未找到符合规则的PDF文件")
            self.preview_btn.setEnabled(False)
            self.process_btn.setEnabled(False)
            return

        # 添加文件到列表
        for pdf_file in valid_pdfs:
            item = QListWidgetItem(pdf_file)
            item.setToolTip(f"文件路径: {os.path.join(self.current_folder, pdf_file)}")
            self.identified_list.addItem(item)

        self.status_bar.showMessage(f"找到 {len(valid_pdfs)} 个符合规则的PDF文件")

    def on_extract_option_changed(self):
        """提取选项改变时的处理"""
        if self.current_folder:
            # 清空预览和结果列表
            self.preview_list.clear()
            self.result_list.clear()
            self.process_btn.setEnabled(False)

    def preview_processing(self):
        """预览处理结果"""
        if not self.current_folder:
            return

        # 确定提取类型
        extract_type = 'platform' if self.platform_radio.isChecked() else 'delivery'

        # 获取预览结果
        self.preview_results = self.parser.preview_rename(self.current_folder, extract_type)

        # 清空预览列表
        self.preview_list.clear()

        if not self.preview_results:
            self.preview_list.addItem("没有可处理的文件")
            self.process_btn.setEnabled(False)
            return

        # 显示预览结果
        for old_name, new_name in self.preview_results:
            item_text = f"{old_name} → {new_name}"
            item = QListWidgetItem(item_text)
            item.setToolTip(f"原文件名: {old_name}\n新文件名: {new_name}")
            self.preview_list.addItem(item)

        self.process_btn.setEnabled(True)
        self.status_bar.showMessage(f"预览完成，将处理 {len(self.preview_results)} 个文件")

    def start_processing(self):
        """开始处理文件"""
        if not self.current_folder or not self.preview_results:
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认处理",
            f"确定要重命名 {len(self.preview_results)} 个文件吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 禁用按钮，显示进度条
        self.process_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        self.select_folder_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 确定提取类型
        extract_type = 'platform' if self.platform_radio.isChecked() else 'delivery'

        # 创建并启动处理线程
        self.process_thread = FileProcessThread(self.current_folder, extract_type)
        self.process_thread.process_completed.connect(self.on_process_completed)
        self.process_thread.start()

        self.status_bar.showMessage("正在处理文件...")

    def on_process_completed(self, success_count, fail_count, errors):
        """处理完成的回调"""
        # 隐藏进度条，恢复按钮状态
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        self.select_folder_btn.setEnabled(True)

        # 显示结果
        self.result_list.clear()

        if success_count > 0:
            success_item = QListWidgetItem(f"✓ 成功处理 {success_count} 个文件")
            success_item.setForeground(QColor("green"))
            self.result_list.addItem(success_item)

        if fail_count > 0:
            fail_item = QListWidgetItem(f"✗ 失败 {fail_count} 个文件")
            fail_item.setForeground(QColor("red"))
            self.result_list.addItem(fail_item)

        # 显示错误信息
        for error in errors:
            error_item = QListWidgetItem(f"错误: {error}")
            error_item.setForeground(QColor("red"))
            self.result_list.addItem(error_item)

        # 更新状态栏
        if fail_count == 0:
            self.status_bar.showMessage(f"处理完成！成功处理 {success_count} 个文件")
            # 显示成功消息
            QMessageBox.information(
                self, "处理完成",
                f"成功重命名 {success_count} 个文件！"
            )
        else:
            self.status_bar.showMessage(f"处理完成，成功 {success_count} 个，失败 {fail_count} 个")
            # 显示警告消息
            QMessageBox.warning(
                self, "处理完成",
                f"处理完成！\n成功: {success_count} 个文件\n失败: {fail_count} 个文件\n\n请查看处理结果列表了解详情。"
            )

        # 重新加载文件列表以反映更改
        self.load_pdf_files()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("PDF面单命名转换器")
    app.setApplicationVersion("1.0")

    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))

    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
