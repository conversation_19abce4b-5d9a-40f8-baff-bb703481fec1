# -*- coding: utf-8 -*-
"""
PDF面单命名转换器配置文件
包含应用程序的各种配置选项
"""

# 应用程序信息
APP_NAME = "PDF面单命名转换器"
APP_VERSION = "1.0"
APP_AUTHOR = "AI Assistant"

# 正则表达式模式配置
REGEX_PATTERNS = {
    # 平台单号模式：2-4个字母开头，后跟8-15位字母数字组合
    'platform': r'([A-Z]{2,4}[0-9A-Z]{8,15})',
    
    # 派送单号模式：GF开头，后跟10-15位数字
    'delivery': r'(GF[0-9]{10,15})',
    
    # 可以根据需要添加更多模式
    # 'custom_pattern': r'(CUSTOM[0-9A-Z]{10,20})',
}

# 文件处理配置
FILE_CONFIG = {
    # 支持的文件扩展名
    'supported_extensions': ['.pdf'],
    
    # 最大文件名长度
    'max_filename_length': 255,
    
    # 是否区分大小写
    'case_sensitive': False,
}

# UI配置
UI_CONFIG = {
    # 窗口默认大小
    'default_window_size': (1000, 700),
    
    # 最小窗口大小
    'min_window_size': (800, 600),
    
    # 默认字体
    'default_font': "Microsoft YaHei",
    'default_font_size': 9,
    
    # 颜色主题
    'colors': {
        'primary': '#4CAF50',
        'primary_hover': '#45a049',
        'primary_pressed': '#3d8b40',
        'warning': '#ff9800',
        'warning_hover': '#f57c00',
        'warning_pressed': '#ef6c00',
        'success': 'green',
        'error': 'red',
        'background': '#f5f5f5',
        'text': '#333333',
        'border': '#cccccc',
    }
}

# 错误消息配置
ERROR_MESSAGES = {
    'no_folder_selected': '请先选择一个文件夹',
    'no_valid_files': '未找到符合规则的PDF文件',
    'no_preview_results': '没有可处理的文件，请先进行预览',
    'file_access_error': '文件访问错误',
    'rename_failed': '文件重命名失败',
    'invalid_file_format': '不支持的文件格式',
    'folder_not_exists': '指定的文件夹不存在',
}

# 成功消息配置
SUCCESS_MESSAGES = {
    'folder_selected': '文件夹选择成功',
    'preview_completed': '预览完成',
    'processing_completed': '处理完成',
    'files_renamed': '文件重命名成功',
}

# 日志配置
LOG_CONFIG = {
    'enable_logging': True,
    'log_level': 'INFO',
    'log_file': 'pdf_converter.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 性能配置
PERFORMANCE_CONFIG = {
    # 单次处理的最大文件数量
    'max_files_per_batch': 1000,
    
    # 线程池大小
    'thread_pool_size': 4,
    
    # 处理超时时间（秒）
    'processing_timeout': 300,
}

# 安全配置
SECURITY_CONFIG = {
    # 是否在处理前显示确认对话框
    'confirm_before_processing': True,
    
    # 是否创建备份
    'create_backup': False,
    
    # 备份文件夹名称
    'backup_folder_name': 'backup',
}

# 获取配置的辅助函数
def get_regex_pattern(pattern_type):
    """获取指定类型的正则表达式模式"""
    return REGEX_PATTERNS.get(pattern_type, '')

def get_ui_color(color_name):
    """获取UI颜色"""
    return UI_CONFIG['colors'].get(color_name, '#000000')

def get_error_message(error_type):
    """获取错误消息"""
    return ERROR_MESSAGES.get(error_type, '未知错误')

def get_success_message(success_type):
    """获取成功消息"""
    return SUCCESS_MESSAGES.get(success_type, '操作成功')

# 验证配置的函数
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证正则表达式
    import re
    for pattern_name, pattern in REGEX_PATTERNS.items():
        try:
            re.compile(pattern)
        except re.error as e:
            errors.append(f"正则表达式 '{pattern_name}' 无效: {e}")
    
    # 验证文件扩展名
    for ext in FILE_CONFIG['supported_extensions']:
        if not ext.startswith('.'):
            errors.append(f"文件扩展名格式错误: {ext}")
    
    # 验证窗口大小
    if len(UI_CONFIG['default_window_size']) != 2:
        errors.append("默认窗口大小配置错误")
    
    if len(UI_CONFIG['min_window_size']) != 2:
        errors.append("最小窗口大小配置错误")
    
    return errors

# 在模块加载时验证配置
if __name__ == "__main__":
    validation_errors = validate_config()
    if validation_errors:
        print("配置验证失败:")
        for error in validation_errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
