# -*- coding: utf-8 -*-
"""
简单测试脚本
测试PDF解析器的基本功能
"""

from pdf_parser import PDFParser

def test_basic_functionality():
    """测试基本功能"""
    parser = PDFParser()
    
    print("PDF面单命名转换器 - 功能测试")
    print("=" * 40)
    
    # 测试文件名
    test_files = [
        "面单_GSU1WQ30B000PSY_GF6457466550016.pdf",
        "订单_ABC123456789_GF1234567890123.pdf",
        "invalid_file.pdf",
        "normal_document.pdf"
    ]
    
    print("1. 测试平台单号提取:")
    print("-" * 30)
    for filename in test_files:
        platform_num = parser.extract_platform_number(filename)
        print(f"文件: {filename}")
        print(f"平台单号: {platform_num or '未找到'}")
        print()
    
    print("2. 测试派送单号提取:")
    print("-" * 30)
    for filename in test_files:
        delivery_num = parser.extract_delivery_number(filename)
        print(f"文件: {filename}")
        print(f"派送单号: {delivery_num or '未找到'}")
        print()
    
    print("3. 测试文件有效性检查:")
    print("-" * 30)
    for filename in test_files:
        is_valid = parser.is_valid_pdf_name(filename)
        status = "有效" if is_valid else "无效"
        print(f"文件: {filename} - 状态: {status}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_basic_functionality()
