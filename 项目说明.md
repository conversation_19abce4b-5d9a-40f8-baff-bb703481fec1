# PDF面单命名转换器

## 项目概述

这是一个专业的PDF面单文件批量重命名工具，能够智能识别面单文件名中的平台单号和派送单号，并根据用户选择进行批量重命名。该工具具有现代化的图形用户界面，操作简单，功能强大。

## 核心功能

### 🎯 智能识别
- 自动识别符合面单命名规则的PDF文件
- 支持多种面单格式（面单_平台单号_派送单号.pdf）
- 过滤无效文件，只处理符合规则的PDF

### 🔄 双模式提取
- **平台单号模式**：提取2-4个字母开头的平台单号（如：GSU1WQ30B000PSY）
- **派送单号模式**：提取GF开头的派送单号（如：GF6457466550016）

### 👀 预览功能
- 处理前可预览所有重命名结果
- 清晰显示原文件名和新文件名的对应关系
- 避免误操作，确保重命名准确性

### 📊 批量处理
- 一键批量重命名多个文件
- 多线程处理，避免界面卡顿
- 实时显示处理进度和结果

### 🎨 美观界面
- 现代化的PyQt6图形用户界面
- 直观的三栏布局：识别面单 | 预览处理 | 处理结果
- 响应式设计，支持窗口缩放

### 🛡️ 安全保护
- 处理前确认对话框，防止误操作
- 自动处理文件名冲突，添加序号避免覆盖
- 完善的错误处理和异常捕获

## 项目结构

```
面单命名转换器/
├── main.py                 # 主程序入口
├── main_window.py          # 主界面UI实现
├── pdf_parser.py           # 核心解析逻辑
├── config.py               # 配置文件
├── logger.py               # 日志模块
├── requirements.txt        # 依赖包列表
├── run.bat                 # Windows启动脚本
├── README.md               # 英文说明文档
├── 项目说明.md             # 中文说明文档
├── create_test_simple.py   # 创建测试文件
├── analyze_test.py         # 分析测试文件
└── test_pdfs/              # 测试文件夹
    ├── waybill_GSU1WQ30B000PSY_GF6457466550016.pdf
    ├── order_ABC123456789_GF1234567890123.pdf
    └── ...
```

## 技术特性

### 开发技术栈
- **编程语言**：Python 3.x（兼容Python 2.7）
- **GUI框架**：PyQt6
- **核心技术**：正则表达式、多线程处理
- **兼容性**：Windows、macOS、Linux

### 核心算法
- 使用正则表达式精确匹配单号格式
- 智能文件名解析和验证
- 安全的文件重命名机制

### 性能优化
- 多线程处理避免UI阻塞
- 高效的文件扫描和过滤
- 内存友好的批量处理

## 安装和使用

### 环境要求
- Python 3.8+ （推荐）或 Python 2.7+
- PyQt6 库

### 安装步骤
1. 确保已安装Python
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

### 使用方法

#### 方法一：使用批处理文件（Windows）
双击 `run.bat` 文件即可启动程序

#### 方法二：命令行启动
```bash
python main.py
```

#### 操作步骤
1. 点击"选择文件夹"按钮，选择包含PDF文件的文件夹
2. 在"识别面单"列中查看找到的符合规则的PDF文件
3. 选择提取模式：
   - "提取平台单号"：将文件重命名为平台单号
   - "提取派送单号"：将文件重命名为派送单号
4. 点击"预览处理"查看重命名结果
5. 确认无误后，点击"开始处理"执行批量重命名
6. 在"处理结果"列中查看处理结果

## 测试和验证

### 创建测试文件
```bash
python create_test_simple.py
```

### 分析测试结果
```bash
python analyze_test.py
```

### 运行单元测试
```bash
python pdf_parser.py
```

## 支持的文件格式

### 有效的面单文件名格式
- `面单_GSU1WQ30B000PSY_GF6457466550016.pdf`
- `订单_ABC123456789_GF1234567890123.pdf`
- `快递_XYZ987654321_GF9876543210987.pdf`
- `发货单_DEF456789012_GF4567890123456.pdf`

### 单号规则
- **平台单号**：2-4个字母开头，后跟8-15位字母数字组合
- **派送单号**：GF开头，后跟10-15位数字

## 商业化考虑

### 目标用户
- 电商企业物流部门
- 快递公司操作人员
- 仓储管理人员
- 个人卖家和小型企业

### 商业价值
- 提高工作效率，减少手动重命名时间
- 降低人为错误，提高数据准确性
- 标准化文件命名，便于管理和查找
- 支持大批量处理，适合企业级应用

### 扩展功能建议
- 支持更多面单格式和快递公司
- 添加文件分类和归档功能
- 集成云存储和数据库
- 提供API接口供其他系统调用
- 添加统计报表和数据分析功能

## 注意事项

- ⚠️ 文件重命名操作不可撤销，请在处理前仔细检查预览结果
- 📁 如果目标文件名已存在，程序会自动添加序号避免冲突
- 🔍 只有符合面单命名规则的PDF文件才会被识别和处理
- 💾 建议在处理前备份重要文件

## 版本信息

- **当前版本**：1.0
- **开发状态**：稳定版本
- **最后更新**：2024年
- **许可证**：仅供学习和个人使用

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看项目文档和README文件
- 运行测试文件验证功能
- 检查日志文件了解详细错误信息

---

**PDF面单命名转换器** - 让文件管理更简单，让工作更高效！
