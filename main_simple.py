# -*- coding: utf-8 -*-
"""
PDF面单命名转换器 - 简化版主程序
专为打包优化，移除了复杂的日志功能
"""

import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QRadioButton, QButtonGroup, QListWidget, QListWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QLabel, QSplitter,
    QGroupBox, QFrame, QStatusBar
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor


class PDFParser:
    """PDF文件名解析器 - 简化版"""
    
    def __init__(self):
        # 面单文件名格式：面单_平台单号_派送单号.pdf
        pass
    
    def extract_platform_number(self, filename):
        """从文件名中提取平台单号"""
        name_without_ext = os.path.splitext(filename)[0]
        
        if not name_without_ext.startswith('面单'):
            return None
        
        parts = name_without_ext.split('_')
        
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            return parts[1]
        
        return None
    
    def extract_delivery_number(self, filename):
        """从文件名中提取派送单号"""
        name_without_ext = os.path.splitext(filename)[0]
        
        if not name_without_ext.startswith('面单'):
            return None
        
        parts = name_without_ext.split('_')
        
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            return parts[2]
        
        return None
    
    def is_valid_pdf_name(self, filename):
        """检查PDF文件名是否符合面单命名规则"""
        if not filename.lower().endswith('.pdf'):
            return False
        
        name_without_ext = os.path.splitext(filename)[0]
        
        if not name_without_ext.startswith('面单'):
            return False
        
        parts = name_without_ext.split('_')
        
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            return True
        
        return False
    
    def get_pdf_files(self, folder_path):
        """获取文件夹中的所有PDF文件"""
        try:
            if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                return []
            
            pdf_files = []
            for filename in os.listdir(folder_path):
                if filename.lower().endswith('.pdf'):
                    file_path = os.path.join(folder_path, filename)
                    if os.path.isfile(file_path):
                        pdf_files.append(filename)
            
            return sorted(pdf_files)
        except Exception as e:
            print("Error getting PDF files: {}".format(e))
            return []
    
    def get_valid_pdf_files(self, folder_path):
        """获取文件夹中符合面单命名规则的PDF文件"""
        all_pdfs = self.get_pdf_files(folder_path)
        valid_pdfs = []
        
        for pdf_file in all_pdfs:
            if self.is_valid_pdf_name(pdf_file):
                valid_pdfs.append(pdf_file)
        
        return valid_pdfs
    
    def preview_rename(self, folder_path, extract_type):
        """预览重命名结果"""
        valid_pdfs = self.get_valid_pdf_files(folder_path)
        preview_results = []
        
        for pdf_file in valid_pdfs:
            if extract_type == 'platform':
                extracted_number = self.extract_platform_number(pdf_file)
            elif extract_type == 'delivery':
                extracted_number = self.extract_delivery_number(pdf_file)
            else:
                continue
            
            if extracted_number:
                new_name = "{}.pdf".format(extracted_number)
                preview_results.append((pdf_file, new_name))
        
        return preview_results
    
    def rename_files(self, folder_path, extract_type):
        """批量重命名文件"""
        preview_results = self.preview_rename(folder_path, extract_type)
        success_count = 0
        fail_count = 0
        errors = []
        
        for old_name, new_name in preview_results:
            try:
                old_path = os.path.join(folder_path, old_name)
                new_path = os.path.join(folder_path, new_name)
                
                # 检查新文件名是否已存在
                if os.path.exists(new_path) and old_path != new_path:
                    # 添加序号避免冲突
                    base_name = os.path.splitext(new_name)[0]
                    counter = 1
                    while os.path.exists(new_path):
                        new_name = "{}_{}".format(base_name, counter) + ".pdf"
                        new_path = os.path.join(folder_path, new_name)
                        counter += 1
                
                # 重命名文件
                os.rename(old_path, new_path)
                success_count += 1
                
            except Exception as e:
                fail_count += 1
                errors.append("重命名 {} 失败: {}".format(old_name, str(e)))
        
        return success_count, fail_count, errors


class FileProcessThread(QThread):
    """文件处理线程"""
    process_completed = pyqtSignal(int, int, list)
    
    def __init__(self, folder_path, extract_type):
        super().__init__()
        self.folder_path = folder_path
        self.extract_type = extract_type
        self.parser = PDFParser()
    
    def run(self):
        """执行文件重命名处理"""
        try:
            success_count, fail_count, errors = self.parser.rename_files(
                self.folder_path, self.extract_type
            )
            self.process_completed.emit(success_count, fail_count, errors)
        except Exception as e:
            self.process_completed.emit(0, 0, ["处理过程中发生错误: {}".format(str(e))])


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.parser = PDFParser()
        self.current_folder = ""
        self.preview_results = []
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("PDF面单命名转换器 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(800, 600)
        
        # 设置应用程序样式
        self.setup_styles()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建左侧控制面板
        self.create_control_panel(main_layout)
        
        # 创建右侧文件列表区域
        self.create_file_lists(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
        
    def setup_styles(self):
        """设置应用程序样式"""
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QRadioButton {
                spacing: 5px;
                color: #333333;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
    
    def create_control_panel(self, main_layout):
        """创建左侧控制面板"""
        control_frame = QFrame()
        control_frame.setFixedWidth(250)
        control_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        control_layout = QVBoxLayout(control_frame)
        
        # 文件夹选择组
        folder_group = QGroupBox("文件夹选择")
        folder_layout = QVBoxLayout(folder_group)
        
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.setMinimumHeight(40)
        folder_layout.addWidget(self.select_folder_btn)
        
        self.folder_label = QLabel("未选择文件夹")
        self.folder_label.setWordWrap(True)
        self.folder_label.setStyleSheet("color: #666; padding: 5px;")
        folder_layout.addWidget(self.folder_label)
        
        control_layout.addWidget(folder_group)
        
        # 提取选项组
        extract_group = QGroupBox("提取选项")
        extract_layout = QVBoxLayout(extract_group)
        
        self.extract_group = QButtonGroup()
        self.platform_radio = QRadioButton("提取平台单号")
        self.delivery_radio = QRadioButton("提取派送单号")
        self.platform_radio.setChecked(True)
        
        self.extract_group.addButton(self.platform_radio, 0)
        self.extract_group.addButton(self.delivery_radio, 1)
        
        extract_layout.addWidget(self.platform_radio)
        extract_layout.addWidget(self.delivery_radio)
        
        control_layout.addWidget(extract_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QVBoxLayout(action_group)
        
        self.preview_btn = QPushButton("预览处理")
        self.preview_btn.setEnabled(False)
        self.preview_btn.setMinimumHeight(35)
        action_layout.addWidget(self.preview_btn)
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setEnabled(False)
        self.process_btn.setMinimumHeight(35)
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
        """)
        action_layout.addWidget(self.process_btn)
        
        control_layout.addWidget(action_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 添加弹性空间
        control_layout.addStretch()
        
        main_layout.addWidget(control_frame)
    
    def create_file_lists(self, main_layout):
        """创建右侧文件列表区域"""
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 识别面单列
        identified_group = QGroupBox("识别面单")
        identified_layout = QVBoxLayout(identified_group)
        self.identified_list = QListWidget()
        self.identified_list.setAlternatingRowColors(True)
        identified_layout.addWidget(self.identified_list)
        
        # 预览处理列
        preview_group = QGroupBox("预览处理")
        preview_layout = QVBoxLayout(preview_group)
        self.preview_list = QListWidget()
        self.preview_list.setAlternatingRowColors(True)
        preview_layout.addWidget(self.preview_list)
        
        # 处理结果列
        result_group = QGroupBox("处理结果")
        result_layout = QVBoxLayout(result_group)
        self.result_list = QListWidget()
        self.result_list.setAlternatingRowColors(True)
        result_layout.addWidget(self.result_list)
        
        splitter.addWidget(identified_group)
        splitter.addWidget(preview_group)
        splitter.addWidget(result_group)
        
        splitter.setSizes([300, 300, 300])
        
        main_layout.addWidget(splitter)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
    def setup_connections(self):
        """设置信号连接"""
        self.select_folder_btn.clicked.connect(self.select_folder)
        self.preview_btn.clicked.connect(self.preview_processing)
        self.process_btn.clicked.connect(self.start_processing)
        self.extract_group.buttonClicked.connect(self.on_extract_option_changed)
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择包含PDF文件的文件夹", ""
        )
        
        if folder_path:
            self.current_folder = folder_path
            self.folder_label.setText("已选择: {}".format(os.path.basename(folder_path)))
            self.load_pdf_files()
            self.preview_btn.setEnabled(True)
            self.status_bar.showMessage("已选择文件夹: {}".format(folder_path))
    
    def load_pdf_files(self):
        """加载PDF文件到识别面单列表"""
        if not self.current_folder:
            return
        
        self.identified_list.clear()
        self.preview_list.clear()
        self.result_list.clear()
        
        valid_pdfs = self.parser.get_valid_pdf_files(self.current_folder)
        
        if not valid_pdfs:
            self.identified_list.addItem("未找到符合规则的PDF文件")
            self.preview_btn.setEnabled(False)
            self.process_btn.setEnabled(False)
            return
        
        for pdf_file in valid_pdfs:
            item = QListWidgetItem(pdf_file)
            item.setToolTip("文件路径: {}".format(os.path.join(self.current_folder, pdf_file)))
            self.identified_list.addItem(item)
        
        self.status_bar.showMessage("找到 {} 个符合规则的PDF文件".format(len(valid_pdfs)))
    
    def on_extract_option_changed(self):
        """提取选项改变时的处理"""
        if self.current_folder:
            self.preview_list.clear()
            self.result_list.clear()
            self.process_btn.setEnabled(False)
            
            extract_type_name = '平台单号' if self.platform_radio.isChecked() else '派送单号'
            self.status_bar.showMessage("已切换到提取{}模式，请点击预览处理查看结果".format(extract_type_name))
    
    def preview_processing(self):
        """预览处理结果"""
        if not self.current_folder:
            return
        
        extract_type = 'platform' if self.platform_radio.isChecked() else 'delivery'
        extract_type_name = '平台单号' if self.platform_radio.isChecked() else '派送单号'
        
        valid_pdfs = self.parser.get_valid_pdf_files(self.current_folder)
        
        self.preview_list.clear()
        
        if not valid_pdfs:
            self.preview_list.addItem("没有可处理的文件")
            self.process_btn.setEnabled(False)
            return
        
        preview_count = 0
        self.preview_results = []
        
        for pdf_file in valid_pdfs:
            if extract_type == 'platform':
                extracted_number = self.parser.extract_platform_number(pdf_file)
            else:
                extracted_number = self.parser.extract_delivery_number(pdf_file)
            
            if extracted_number:
                item_text = "{} → {}".format(pdf_file, extracted_number)
                item = QListWidgetItem(item_text)
                item.setToolTip("原文件名: {}\n提取的{}: {}\n重命名后: {}.pdf".format(
                    pdf_file, extract_type_name, extracted_number, extracted_number))
                self.preview_list.addItem(item)
                
                self.preview_results.append((pdf_file, "{}.pdf".format(extracted_number)))
                preview_count += 1
        
        if preview_count == 0:
            self.preview_list.addItem("没有文件可以提取{}".format(extract_type_name))
            self.process_btn.setEnabled(False)
        else:
            self.process_btn.setEnabled(True)
            self.status_bar.showMessage("预览完成，找到 {} 个可提取{}的文件".format(preview_count, extract_type_name))
    
    def start_processing(self):
        """开始处理文件"""
        if not self.current_folder or not self.preview_results:
            return
        
        reply = QMessageBox.question(
            self, "确认处理", 
            "确定要重命名 {} 个文件吗？\n此操作不可撤销！".format(len(self.preview_results)),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        self.process_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        self.select_folder_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        extract_type = 'platform' if self.platform_radio.isChecked() else 'delivery'
        
        self.process_thread = FileProcessThread(self.current_folder, extract_type)
        self.process_thread.process_completed.connect(self.on_process_completed)
        self.process_thread.start()
        
        self.status_bar.showMessage("正在处理文件...")
    
    def on_process_completed(self, success_count, fail_count, errors):
        """处理完成的回调"""
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        self.select_folder_btn.setEnabled(True)
        
        self.result_list.clear()
        
        if success_count > 0:
            success_item = QListWidgetItem("✓ 成功处理 {} 个文件".format(success_count))
            success_item.setForeground(QColor("green"))
            self.result_list.addItem(success_item)
        
        if fail_count > 0:
            fail_item = QListWidgetItem("✗ 失败 {} 个文件".format(fail_count))
            fail_item.setForeground(QColor("red"))
            self.result_list.addItem(fail_item)
        
        for error in errors:
            error_item = QListWidgetItem("错误: {}".format(error))
            error_item.setForeground(QColor("red"))
            self.result_list.addItem(error_item)
        
        if fail_count == 0:
            self.status_bar.showMessage("处理完成！成功处理 {} 个文件".format(success_count))
            QMessageBox.information(
                self, "处理完成", 
                "成功重命名 {} 个文件！".format(success_count)
            )
        else:
            self.status_bar.showMessage("处理完成，成功 {} 个，失败 {} 个".format(success_count, fail_count))
            QMessageBox.warning(
                self, "处理完成", 
                "处理完成！\n成功: {} 个文件\n失败: {} 个文件\n\n请查看处理结果列表了解详情。".format(success_count, fail_count)
            )
        
        self.load_pdf_files()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("PDF面单命名转换器")
    app.setApplicationVersion("1.0")
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
