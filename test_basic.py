# -*- coding: utf-8 -*-

from pdf_parser import PDFParser

def test_basic_functionality():
    """Test basic functionality"""
    parser = PDFParser()
    
    print("PDF Waybill Naming Converter - Function Test")
    print("=" * 40)
    
    # Test filenames
    test_files = [
        "waybill_GSU1WQ30B000PSY_GF6457466550016.pdf",
        "order_ABC123456789_GF1234567890123.pdf",
        "invalid_file.pdf",
        "normal_document.pdf"
    ]
    
    print("1. Test platform number extraction:")
    print("-" * 30)
    for filename in test_files:
        platform_num = parser.extract_platform_number(filename)
        print("File: {}".format(filename))
        print("Platform number: {}".format(platform_num or 'Not found'))
        print()

    print("2. Test delivery number extraction:")
    print("-" * 30)
    for filename in test_files:
        delivery_num = parser.extract_delivery_number(filename)
        print("File: {}".format(filename))
        print("Delivery number: {}".format(delivery_num or 'Not found'))
        print()

    print("3. Test file validity check:")
    print("-" * 30)
    for filename in test_files:
        is_valid = parser.is_valid_pdf_name(filename)
        status = "Valid" if is_valid else "Invalid"
        print("File: {} - Status: {}".format(filename, status))
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_basic_functionality()
