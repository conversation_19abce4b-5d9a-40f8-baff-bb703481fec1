# PDF面单命名转换器 - 使用说明

## 功能概述

这个工具专门用于处理以"面单"开头的PDF文件，能够智能提取其中的平台单号和派送单号，并批量重命名文件。

## 文件名格式规则

**标准格式**：`面单_平台单号_派送单号.pdf`

**示例**：
- `面单_GSU1WQ30B000PSY_GF6457466550016.pdf`
  - 平台单号：GSU1WQ30B000PSY
  - 派送单号：GF6457466550016

**规则说明**：
- 必须以"面单"开头
- 使用下划线"_"分隔各部分
- 第二部分是平台单号（格式不限）
- 第三部分是派送单号（格式不限）
- 必须是PDF文件（.pdf扩展名）

## 操作步骤

### 1. 启动程序
- 双击 `run.bat` 文件（Windows）
- 或者命令行运行：`python main.py`

### 2. 选择文件夹
- 点击左侧"选择文件夹"按钮
- 选择包含PDF面单文件的文件夹
- 程序会自动扫描并在"识别面单"列中显示符合格式的文件

### 3. 选择提取模式
- **提取平台单号**：将文件重命名为平台单号
- **提取派送单号**：将文件重命名为派送单号

### 4. 预览处理结果
- 点击"预览处理"按钮
- 在"预览处理"列中查看提取结果
- 显示格式：`原文件名 → 提取的单号`
- 鼠标悬停可查看详细信息

### 5. 执行批量重命名
- 确认预览结果无误后，点击"开始处理"
- 程序会弹出确认对话框，确认后执行重命名
- 在"处理结果"列中查看处理结果

## 界面说明

### 左侧控制面板
- **文件夹选择**：选择要处理的PDF文件所在文件夹
- **提取选项**：
  - ○ 提取平台单号：提取文件名中的平台单号部分
  - ○ 提取派送单号：提取文件名中的派送单号部分
- **操作按钮**：
  - 预览处理：查看提取结果，不执行重命名
  - 开始处理：执行批量重命名操作

### 右侧文件列表区域
- **识别面单**：显示找到的符合"面单_*_*.pdf"格式的文件
- **预览处理**：显示根据选择模式提取的单号结果
- **处理结果**：显示重命名操作的成功/失败结果

## 使用示例

### 示例1：提取平台单号
**原文件**：`面单_GSU1WQ30B000PSY_GF6457466550016.pdf`
**选择**：提取平台单号
**预览显示**：`面单_GSU1WQ30B000PSY_GF6457466550016.pdf → GSU1WQ30B000PSY`
**重命名后**：`GSU1WQ30B000PSY.pdf`

### 示例2：提取派送单号
**原文件**：`面单_GSU1WQ30B000PSY_GF6457466550016.pdf`
**选择**：提取派送单号
**预览显示**：`面单_GSU1WQ30B000PSY_GF6457466550016.pdf → GF6457466550016`
**重命名后**：`GF6457466550016.pdf`

## 注意事项

### ⚠️ 重要提醒
- 文件重命名操作**不可撤销**，请务必在处理前仔细检查预览结果
- 建议在处理重要文件前先备份

### 📁 文件处理规则
- 只处理以"面单"开头的PDF文件
- 不符合格式的文件会被自动过滤，不会显示在识别列表中
- 如果目标文件名已存在，程序会自动添加序号（如：ABC123_1.pdf）

### 🔍 有效文件格式
- ✅ `面单_ABC123_GF456789.pdf` - 标准格式
- ✅ `面单_PLATFORM123_DELIVERY456.pdf` - 任意单号格式
- ✅ `面单_短号_长派送单号123456789.pdf` - 长度不限
- ❌ `订单_ABC123_GF456789.pdf` - 不是以"面单"开头
- ❌ `面单_ABC123.pdf` - 缺少派送单号部分
- ❌ `面单_.pdf` - 单号部分为空

## 故障排除

### 问题1：程序无法启动
**解决方案**：
1. 确认已安装Python 3.8+
2. 安装依赖：`pip install -r requirements.txt`
3. 检查PyQt6是否正确安装

### 问题2：找不到符合格式的文件
**解决方案**：
1. 确认文件名以"面单"开头
2. 确认使用下划线分隔
3. 确认有平台单号和派送单号两部分
4. 确认文件扩展名为.pdf

### 问题3：重命名失败
**可能原因**：
- 文件正在被其他程序使用
- 没有文件夹写入权限
- 目标文件名包含非法字符

## 技术特性

- **智能识别**：自动过滤无效文件
- **安全处理**：重命名前确认，避免文件名冲突
- **批量操作**：支持一次处理多个文件
- **实时反馈**：显示处理进度和结果
- **多线程**：避免界面卡顿

## 版本信息

- **版本**：1.0
- **支持Python**：3.8+ (推荐3.11)
- **GUI框架**：PyQt6
- **支持平台**：Windows, macOS, Linux

---

**使用愉快！如有问题请查看日志文件或联系技术支持。**
