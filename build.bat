@echo off
chcp 65001 >nul
echo ====================================
echo    PDF面单命名转换器 - 打包脚本
echo ====================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 当前Python版本:
python --version
echo.

REM 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
) else (
    echo PyInstaller已安装
)

REM 检查PyQt6是否安装
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo PyQt6未安装，正在安装...
    pip install PyQt6
    if errorlevel 1 (
        echo 错误: PyQt6安装失败
        pause
        exit /b 1
    )
) else (
    echo PyQt6已安装
)

echo.
echo 开始打包...
echo.

REM 清理之前的打包结果
if exist "dist" (
    echo 清理旧的打包文件...
    rmdir /s /q dist
)

if exist "build" (
    rmdir /s /q build
)

REM 使用PyInstaller打包
echo 正在打包，请稍候...
pyinstaller --clean pdf_converter.spec

REM 检查打包结果
if exist "dist\PDF面单命名转换器.exe" (
    echo.
    echo ====================================
    echo          打包成功！
    echo ====================================
    echo.
    echo 可执行文件位置: dist\PDF面单命名转换器.exe
    echo 文件大小:
    dir "dist\PDF面单命名转换器.exe" | findstr "PDF面单命名转换器.exe"
    echo.
    echo 您可以将此exe文件复制到任何Windows电脑上运行
    echo 无需安装Python或其他依赖
    echo.
    
    REM 询问是否立即运行
    set /p run_now="是否立即运行打包后的程序？(y/n): "
    if /i "%run_now%"=="y" (
        echo 启动程序...
        start "" "dist\PDF面单命名转换器.exe"
    )
) else (
    echo.
    echo ====================================
    echo          打包失败！
    echo ====================================
    echo.
    echo 请检查错误信息并重试
    echo 常见问题：
    echo 1. 确保所有依赖包已正确安装
    echo 2. 确保没有其他程序占用相关文件
    echo 3. 检查Python版本是否兼容
)

echo.
pause
