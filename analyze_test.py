# -*- coding: utf-8 -*-

from pdf_parser import PDFParser
import os

def analyze_test_files():
    """Analyze test files"""
    test_folder = "test_pdfs"
    
    if not os.path.exists(test_folder):
        print("Test folder does not exist, please create test files first")
        return
    
    parser = PDFParser()
    
    print("Test File Analysis Report")
    print("=" * 60)
    
    # Get all PDF files
    all_pdfs = parser.get_pdf_files(test_folder)
    valid_pdfs = parser.get_valid_pdf_files(test_folder)
    
    print("Total PDF files: {}".format(len(all_pdfs)))
    print("Valid waybill files: {}".format(len(valid_pdfs)))
    print()
    
    print("Detailed file analysis:")
    print("-" * 60)
    
    for pdf_file in all_pdfs:
        platform_num = parser.extract_platform_number(pdf_file)
        delivery_num = parser.extract_delivery_number(pdf_file)
        is_valid = parser.is_valid_pdf_name(pdf_file)
        
        status = "Valid" if is_valid else "Invalid"
        
        print("File: {}".format(pdf_file))
        print("  Status: {}".format(status))
        print("  Platform number: {}".format(platform_num or 'Not found'))
        print("  Delivery number: {}".format(delivery_num or 'Not found'))
        print()
    
    # Preview rename results
    print("Platform number rename preview:")
    print("-" * 40)
    platform_preview = parser.preview_rename(test_folder, 'platform')
    for old_name, new_name in platform_preview:
        print("  {} -> {}".format(old_name, new_name))
    
    print()
    print("Delivery number rename preview:")
    print("-" * 40)
    delivery_preview = parser.preview_rename(test_folder, 'delivery')
    for old_name, new_name in delivery_preview:
        print("  {} -> {}".format(old_name, new_name))

if __name__ == "__main__":
    analyze_test_files()
