# -*- coding: utf-8 -*-

import os

def create_test_files():
    """Create test files directly"""
    # Create test folder
    test_folder = "test_pdfs"
    if not os.path.exists(test_folder):
        os.makedirs(test_folder)
    
    # Test files list - correct format: 面单_平台单号_派送单号.pdf
    test_files = [
        # Valid waybill files (correct format)
        "面单_GSU1WQ30B000PSY_GF6457466550016.pdf",
        "面单_ABC123456789_GF1234567890123.pdf",
        "面单_XYZ987654321_GF9876543210987.pdf",
        "面单_DEF456789012_GF4567890123456.pdf",
        "面单_HIJ789012345_GF7890123456789.pdf",
        "面单_MNO012345678_GF1111222233334.pdf",
        "面单_PQR345678901_GF5555666677778.pdf",
        "面单_STU654321098_GF8888999900001.pdf",
        "面单_VWX321098765_GF2222333344445.pdf",
        "面单_YZA098765432_GF7777888899990.pdf",

        # Invalid files (won't be processed)
        "订单_ABC123456789_GF1234567890123.pdf",  # 不是以"面单"开头
        "快递_XYZ987654321_GF9876543210987.pdf",  # 不是以"面单"开头
        "面单_ONLY_ONE_PART.pdf",                # 只有一个下划线
        "面单_.pdf",                            # 空的单号部分
        "面单_ABC_.pdf",                        # 派送单号为空
        "面单__GF123456789.pdf",                # 平台单号为空
        "normal_document.pdf",                   # 普通文档
        "manual.pdf",                           # 说明书
        "invalid_format.pdf",                   # 无效格式
        "no_numbers_here.pdf",                  # 没有单号
    ]
    
    print("Creating test files in folder: {}".format(os.path.abspath(test_folder)))
    print("=" * 50)
    
    created_count = 0
    for filename in test_files:
        file_path = os.path.join(test_folder, filename)
        
        # Create empty PDF files (for testing filename only)
        try:
            with open(file_path, 'w') as f:
                f.write("%PDF-1.4\n% Test file: {}\n%%EOF".format(filename))
            
            print("Created: {}".format(filename))
            created_count += 1
            
        except Exception as e:
            print("Failed to create: {} - Error: {}".format(filename, e))
    
    print("=" * 50)
    print("Test files created! Total: {} files".format(created_count))
    print("Test folder location: {}".format(os.path.abspath(test_folder)))
    print()
    print("Usage instructions:")
    print("1. Run 'python main.py' to start the program")
    print("2. Click 'Select Folder' button")
    print("3. Select the 'test_pdfs' folder")
    print("4. Choose extraction mode")
    print("5. Click 'Preview Processing' to see results")
    print("6. Click 'Start Processing' to rename files")

if __name__ == "__main__":
    create_test_files()
