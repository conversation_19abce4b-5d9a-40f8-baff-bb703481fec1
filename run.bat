@echo off
chcp 65001 >nul
echo ====================================
echo    PDF面单命名转换器 v1.0
echo ====================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查Python环境...
python --version

REM 检查依赖包是否安装
echo.
echo 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已安装
)

REM 运行程序
echo.
echo 启动PDF面单命名转换器...
echo.
python main.py

REM 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    pause
)
