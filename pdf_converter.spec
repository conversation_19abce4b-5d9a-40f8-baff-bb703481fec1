# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 分析主程序
a = Analysis(
    ['main_simple.py'],
    pathex=[project_root],
    binaries=[],
    datas=[
        # 不包含额外数据文件，减小打包体积
    ],
    hiddenimports=[
        # PyQt6相关模块
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.sip',
        # 确保包含所有必要的PyQt6子模块
        'PyQt6.QtCore._QByteArray',
        'PyQt6.QtCore._QString',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'notebook',
        'pytest',
        'unittest',
        'doctest',
        'pdb',
        'profile',
        'pstats',
        'cProfile',
        'trace',
        'timeit',
        'sqlite3',
        'xml',
        'email',
        'http',
        'urllib',
        'ftplib',
        'smtplib',
        'poplib',
        'imaplib',
        'telnetlib',
        'socketserver',
        'xmlrpc',
        'multiprocessing',
        'concurrent',
        'asyncio',
        'queue',
        'threading',
        'subprocess',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'pkg_resources',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 过滤掉不需要的文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建单文件可执行程序
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PDF面单命名转换器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用UPX压缩（如果可用）
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # Windows特定选项
    version='version_info.txt',  # 版本信息文件（可选）
    uac_admin=False,  # 不需要管理员权限
    uac_uiaccess=False,
    # 图标文件（如果有的话）
    # icon='icon.ico',
)

# 如果需要创建目录分发版本，取消注释以下代码
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='PDF面单命名转换器'
# )
