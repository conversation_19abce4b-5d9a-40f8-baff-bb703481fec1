# -*- coding: utf-8 -*-
"""
PDF面单文件名解析器
用于从PDF文件名中提取平台单号和派送单号
"""
import re
import os
try:
    from typing import Optional, Tuple, List
except ImportError:
    # For older Python versions
    Optional = None
    Tuple = None
    List = None

try:
    from pathlib import Path
except ImportError:
    # For older Python versions, use os.path
    Path = None


class PDFParser:
    """PDF文件名解析器"""
    
    def __init__(self):
        # 面单文件名格式：面单_平台单号_派送单号.pdf
        # 不需要复杂的正则表达式，直接按下划线分割即可
        pass
        
    def extract_platform_number(self, filename):
        """
        从文件名中提取平台单号
        格式：面单_平台单号_派送单号.pdf

        Args:
            filename: PDF文件名

        Returns:
            提取到的平台单号，如果没有找到则返回None
        """
        # 移除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 检查是否以"面单"开头
        if not name_without_ext.startswith('面单'):
            return None

        # 按下划线分割
        parts = name_without_ext.split('_')

        # 格式应该是：面单_平台单号_派送单号
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            # 返回中间的平台单号（第二部分）
            return parts[1]

        return None
    
    def extract_delivery_number(self, filename):
        """
        从文件名中提取派送单号
        格式：面单_平台单号_派送单号.pdf

        Args:
            filename: PDF文件名

        Returns:
            提取到的派送单号，如果没有找到则返回None
        """
        # 移除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 检查是否以"面单"开头
        if not name_without_ext.startswith('面单'):
            return None

        # 按下划线分割
        parts = name_without_ext.split('_')

        # 格式应该是：面单_平台单号_派送单号
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            # 返回末尾的派送单号（第三部分）
            return parts[2]

        return None
    
    def is_valid_pdf_name(self, filename):
        """
        检查PDF文件名是否符合面单命名规则
        格式：面单_平台单号_派送单号.pdf

        Args:
            filename: PDF文件名

        Returns:
            如果符合规则返回True，否则返回False
        """
        if not filename.lower().endswith('.pdf'):
            return False

        # 移除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 检查是否以"面单"开头
        if not name_without_ext.startswith('面单'):
            return False

        # 按下划线分割，应该至少有3部分：面单_平台单号_派送单号
        parts = name_without_ext.split('_')

        # 检查格式：至少3部分，且平台单号和派送单号不为空
        if len(parts) >= 3 and parts[1].strip() and parts[2].strip():
            return True

        return False
    
    def get_pdf_files(self, folder_path):
        """
        获取文件夹中的所有PDF文件
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            PDF文件名列表
        """
        try:
            if Path is not None:
                # Use pathlib if available
                folder = Path(folder_path)
                if not folder.exists() or not folder.is_dir():
                    return []

                pdf_files = []
                for file_path in folder.glob('*.pdf'):
                    if file_path.is_file():
                        pdf_files.append(file_path.name)

                return sorted(pdf_files)
            else:
                # Fallback to os.path for older Python versions
                if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                    return []

                pdf_files = []
                for filename in os.listdir(folder_path):
                    if filename.lower().endswith('.pdf'):
                        file_path = os.path.join(folder_path, filename)
                        if os.path.isfile(file_path):
                            pdf_files.append(filename)

                return sorted(pdf_files)
        except Exception as e:
            print("Error getting PDF files: {}".format(e))
            return []
    
    def get_valid_pdf_files(self, folder_path):
        """
        获取文件夹中符合面单命名规则的PDF文件
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            符合规则的PDF文件名列表
        """
        all_pdfs = self.get_pdf_files(folder_path)
        valid_pdfs = []
        
        for pdf_file in all_pdfs:
            if self.is_valid_pdf_name(pdf_file):
                valid_pdfs.append(pdf_file)
        
        return valid_pdfs
    
    def preview_rename(self, folder_path, extract_type):
        """
        预览重命名结果
        
        Args:
            folder_path: 文件夹路径
            extract_type: 提取类型 ('platform' 或 'delivery')
            
        Returns:
            (原文件名, 新文件名) 的元组列表
        """
        valid_pdfs = self.get_valid_pdf_files(folder_path)
        preview_results = []
        
        for pdf_file in valid_pdfs:
            if extract_type == 'platform':
                extracted_number = self.extract_platform_number(pdf_file)
            elif extract_type == 'delivery':
                extracted_number = self.extract_delivery_number(pdf_file)
            else:
                continue
            
            if extracted_number:
                new_name = "{}.pdf".format(extracted_number)
                preview_results.append((pdf_file, new_name))
        
        return preview_results
    
    def rename_files(self, folder_path, extract_type):
        """
        批量重命名文件
        
        Args:
            folder_path: 文件夹路径
            extract_type: 提取类型 ('platform' 或 'delivery')
            
        Returns:
            (成功数量, 失败数量, 错误信息列表)
        """
        preview_results = self.preview_rename(folder_path, extract_type)
        success_count = 0
        fail_count = 0
        errors = []
        
        for old_name, new_name in preview_results:
            try:
                if Path is not None:
                    # Use pathlib if available
                    folder = Path(folder_path)
                    old_path = folder / old_name
                    new_path = folder / new_name

                    # Check if new filename already exists
                    if new_path.exists() and new_path != old_path:
                        # Add counter if file exists
                        base_name = os.path.splitext(new_name)[0]
                        counter = 1
                        while new_path.exists():
                            new_name = "{}_{}".format(base_name, counter) + ".pdf"
                            new_path = folder / new_name
                            counter += 1

                    # Rename file
                    old_path.rename(new_path)
                else:
                    # Fallback to os.path for older Python versions
                    old_path = os.path.join(folder_path, old_name)
                    new_path = os.path.join(folder_path, new_name)

                    # Check if new filename already exists
                    if os.path.exists(new_path) and old_path != new_path:
                        # Add counter if file exists
                        base_name = os.path.splitext(new_name)[0]
                        counter = 1
                        while os.path.exists(new_path):
                            new_name = "{}_{}".format(base_name, counter) + ".pdf"
                            new_path = os.path.join(folder_path, new_name)
                            counter += 1

                    # Rename file
                    os.rename(old_path, new_path)

                success_count += 1

            except Exception as e:
                fail_count += 1
                errors.append("Rename {} failed: {}".format(old_name, str(e)))
        
        return success_count, fail_count, errors


# 测试函数
if __name__ == "__main__":
    parser = PDFParser()
    
    # Test filename parsing
    test_filename = "面单_GSU1WQ30B000PSY_GF6457466550016.pdf"
    print("Test filename: {}".format(test_filename))
    print("Platform number: {}".format(parser.extract_platform_number(test_filename)))
    print("Delivery number: {}".format(parser.extract_delivery_number(test_filename)))
    print("Is valid: {}".format(parser.is_valid_pdf_name(test_filename)))
